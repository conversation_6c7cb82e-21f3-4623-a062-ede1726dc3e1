# Test Documentation

## 📁 Test Directory Structure

```
test/
├── main/                    # Main process tests
│   └── eventbus/           # EventBus tests
│       └── eventbus.test.ts
├── renderer/               # Renderer process tests
│   └── shell/              # Shell application tests
│       ├── App.test.ts     # App component test
│       └── main.test.ts    # Entry file test
├── setup.ts                # Main process test setup
├── setup.renderer.ts       # Renderer process test setup
└── README.md              # This document
```

## 🚀 Getting Started

### Install Test Dependencies

First, install the dependencies required for Vue component testing:

```bash
# Install Vue test tools
npm install -D @vue/test-utils jsdom

# Or use yarn
yarn add -D @vue/test-utils jsdom
```

### Run Tests

```bash
# Run all tests
npm test

# Run main process tests
npm run test:main

# Run renderer process tests
npm run test:renderer

# Run tests and generate coverage report
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## 📝 Test Scripts

Add the following test scripts to `package.json`:

```json
{
  "scripts": {
    "test": "vitest",
    "test:main": "vitest --config vitest.config.ts test/main",
    "test:renderer": "vitest --config vitest.config.renderer.ts test/renderer",
    "test:coverage": "vitest --coverage",
    "test:watch": "vitest --watch",
    "test:ui": "vitest --ui"
  }
}
```

## 🧪 Test Types

### Main Process Tests
- **Environment**: Node.js
- **Config**: `vitest.config.ts`
- **Focus**: EventBus, Presenter layer, utility functions

### Renderer Process Tests
- **Environment**: jsdom
- **Config**: `vitest.config.renderer.ts`
- **Focus**: Vue components, Store, Composables

## 📊 Test Coverage

Generate a test coverage report:

```bash
npm run test:coverage
```

The coverage report will be generated in:
- `coverage/` - Main process coverage
- `coverage/renderer/` - Renderer process coverage

Open `coverage/index.html` to view the detailed coverage report.

## 🔧 Configuration Files

### vitest.config.ts
Main process test configuration, using the Node.js environment.

### vitest.config.renderer.ts
Renderer process test configuration, using the jsdom environment to support Vue component testing.

### test/setup.ts
Global setup for main process tests, including mocks for Electron modules.

### test/setup.renderer.ts
Global setup for renderer process tests, including mocks for Vue-related dependencies.

## 📋 Testing Guidelines

### File Naming
- Test files use the `.test.ts` or `.spec.ts` suffix
- Keep the same directory structure as the source files

### Test Descriptions
- Use Chinese to describe test scenarios
- Group by functionality using `describe`
- Describe specific test cases with `it`

### Example Test Structure
```typescript
describe('Module Name', () => {
  beforeEach(() => {
    // Setup before each test
  })

  describe('Functionality Group', () => {
    it('should be able to perform an action', () => {
      // Arrange - Prepare test data
      // Act - Execute the operation under test
      // Assert - Verify the result
    })
  })
})
```

## 🐛 Debugging Tests

### Debug a Single Test
```bash
# Run a specific test file
npx vitest test/main/eventbus/eventbus.test.ts

# Run a specific test case
npx vitest -t "should correctly send events to the main process"
```

### Debug Configuration
Add a debug configuration to VSCode (`.vscode/launch.json`):

```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug Vitest Tests",
  "skipFiles": ["<node_internals>/**"],
  "program": "${workspaceRoot}/node_modules/vitest/vitest.mjs",
  "args": ["--run", "${relativeFile}"],
  "smartStep": true,
  "console": "integratedTerminal"
}
```

## 🎯 Best Practices

### Mocking Strategy
1. **External Dependencies**: Fully mock (network requests, file system)
2. **Internal Modules**: Selective mocking (complex dependencies, unstable components)
3. **Pure Functions**: Prefer real implementation

### Test Data
- Use simple and clear test data
- Avoid using real sensitive data
- Consider using factory functions to generate test data

### Assertion Tips
```typescript
// Recommended assertion methods
expect(result).toBe(expected)           // Strict equality
expect(result).toEqual(expected)        // Deep equality
expect(fn).toHaveBeenCalledWith(args)   // Function call verification
expect(element).toBeInTheDocument()     // DOM presence verification
```

## 📚 Related Resources

- [Vitest Official Documentation](https://vitest.dev/)  
- [Vue Test Utils Documentation](https://test-utils.vuejs.org/)  
- [Testing Library Best Practices](https://testing-library.com/docs/guiding-principles/)  

## ❓ Frequently Asked Questions

### Q: How to test asynchronous operations?
```typescript
it('should handle async operations', async () => {
  const result = await asyncFunction()
  expect(result).toBe(expected)
})
```

### Q: How to test error handling?
```typescript
it('should handle errors properly', () => {
  expect(() => errorFunction()).toThrow('Expected error message')
})
```

### Q: How to mock a module?
```typescript
vi.mock('./module', () => ({
  exportedFunction: vi.fn()
}))
```