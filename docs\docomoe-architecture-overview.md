
---

# Docomoe Project Architecture Overview

This document provides a high-level overview of the Docomoe project architecture, helping developers quickly understand the project’s structure and execution flow.

---

## 🏗️ Architecture Diagram

```mermaid
graph TB
    subgraph "Electron Main Process"
        MainEntry[index.ts (Main Entry)]
        EventBus[EventBus]

        subgraph "Core Presenter Layer"
            WindowP[WindowPresenter<br/>Window Management]
            TabP[TabPresenter<br/>Tab Management]
            ThreadP[ThreadPresenter<br/>Session Management]
            ConfigP[ConfigPresenter<br/>Config Management]
            MCPP[McpPresenter<br/>MCP Tool Management]
            LLMP[LLMProviderPresenter<br/>LLM Providers]
            SyncP[SyncPresenter<br/>Data Sync]
            FileP[FilePresenter<br/>File Management]
            UpgradeP[UpgradePresenter<br/>App Update]
        end

        subgraph "Low-Level Services"
            SqliteP[SqlitePresenter<br/>Database]
            TrayP[TrayPresenter<br/>System Tray]
            NotificationP[NotificationPresenter<br/>Notifications]
            DeeplinkP[DeeplinkPresenter<br/>Deeplink]
        end

        ContextMenu[ContextMenuHelper<br/>Context Menu]
    end

    subgraph "Electron Renderer Process"
        subgraph "Multi-Window Architecture"
            subgraph "Window Shell"
                ShellHTML[shell/index.html]
                TabBar[TabBar.vue<br/>Tab Bar UI]
                ShellVue[Vue Instance<br/>Lightweight Window Mgmt]
            end

            subgraph "Tab Content"
                ContentHTML[src/index.html]
                MainApp[Main App UI]

                subgraph "Vue Application Layer"
                    Router[Vue Router<br/>Routing]
                    Pinia[Pinia Store<br/>State Management]

                    subgraph "Page Components"
                        ChatView[ChatView.vue<br/>Chat UI]
                        SettingsView[SettingsView.vue<br/>Settings Page]
                        McpView[McpView.vue<br/>MCP Mgmt]
                        ThreadView[ThreadView.vue<br/>Session List]
                    end
                end
            end
        end
    end

    subgraph "Preload Scripts"
        PreloadAPI[Preload API<br/>Secure IPC Bridge]
    end

    subgraph "External Services & Integrations"
        subgraph "MCP Ecosystem"
            MCPServers[MCP Servers<br/>External Tools]
            MCPClients[MCP Clients<br/>Connection Mgmt]
            MCPTransport[Transport Layer<br/>Stdio/SSE/HTTP]
        end

        subgraph "LLM Providers"
            OpenAI[OpenAI API]
            Anthropic[Anthropic Claude]
            Gemini[Google Gemini]
            LocalLLM[Local LLM<br/>Llama.cpp, etc.]
        end

        subgraph "Data Storage"
            LocalDB[(SQLite Database)]
            ConfigFiles[Config Files<br/>Electron Store]
            FileSystem[File System<br/>User Files]
        end
    end

    %% Connections
    MainEntry --> EventBus
    EventBus --> WindowP
    EventBus --> TabP
    EventBus --> ThreadP
    EventBus --> ConfigP
    EventBus --> MCPP

    WindowP --> TabP
    TabP --> ShellHTML
    TabP --> ContentHTML

    ThreadP --> ChatView
    ConfigP --> SettingsView
    MCPP --> McpView

    %% IPC
    ShellVue -.->|IPC| TabP
    MainApp -.->|IPC| ThreadP
    SettingsView -.->|IPC| ConfigP
    McpView -.->|IPC| MCPP

    %% Dataflow
    MCPP --> MCPServers
    LLMP --> OpenAI
    LLMP --> Anthropic
    LLMP --> Gemini
    LLMP --> LocalLLM

    SqliteP --> LocalDB
    ConfigP --> ConfigFiles
    FileP --> FileSystem

    %% Event System
    WindowP -.->|event| EventBus
    TabP -.->|event| EventBus
    ThreadP -.->|event| EventBus
    ConfigP -.->|event| EventBus

    classDef mainProcess fill:#e1f5fe
    classDef renderer fill:#f3e5f5
    classDef external fill:#e8f5e8
    classDef preload fill:#fff3e0

    class MainEntry,EventBus,WindowP,TabP,ThreadP,ConfigP,MCPP,LLMP,SyncP,FileP,UpgradeP,SqliteP,TrayP,NotificationP,DeeplinkP,ContextMenu mainProcess
    class ShellHTML,TabBar,ShellVue,ContentHTML,MainApp,Router,Pinia,ChatView,SettingsView,McpView,ThreadView renderer
    class MCPServers,MCPClients,MCPTransport,OpenAI,Anthropic,Gemini,LocalLLM,LocalDB,ConfigFiles,FileSystem external
    class PreloadAPI preload
```

---

## 🔄 Core Operation Flows

### 1. **App Startup Process**

```mermaid
sequenceDiagram
    participant App as Electron App
    participant Main as Main Process
    participant Presenters as Presenter Layer
    participant Window as Window Mgmt
    participant Shell as Window Shell
    participant Content as Tab Content

    App->>Main: app.whenReady()
    Main->>Presenters: Initialize all Presenters
    Presenters->>Presenters: Register Event Listeners
    Main->>Window: createWindow()
    Window->>Shell: Load shell/index.html
    Shell->>Content: Create first tab
    Content->>Content: Load main app src/index.html

    Note over Main,Content: App ready, user interaction begins
```

### 2. **Multi-Window & Tab Management**

```mermaid
sequenceDiagram
    participant User as User
    participant Shell as Window Shell
    participant TabP as TabPresenter
    participant WindowP as WindowPresenter
    participant Content as Tab Content

    User->>Shell: Click "New Tab"
    Shell->>TabP: Request to create new tab
    TabP->>TabP: Create WebContentsView
    TabP->>Content: Load tab content
    TabP->>Shell: Update tab bar UI

    User->>Shell: Drag tab to new window
    Shell->>TabP: Tab move request
    TabP->>WindowP: Create new window
    TabP->>TabP: detachTab & attachTab
    TabP->>Shell: Update both window tab bars
```

### 3. **MCP Tool Invocation Flow**

```mermaid
sequenceDiagram
    participant User as User
    participant Chat as Chat View
    participant ThreadP as ThreadPresenter
    participant MCPP as McpPresenter
    participant LLM as LLM Provider
    participant Tool as MCP Tool

    User->>Chat: Send message
    Chat->>ThreadP: Process user message
    ThreadP->>LLM: Send message to LLM
    LLM->>LLM: Analyze if tool is needed
    LLM->>MCPP: Request tool definitions
    MCPP->>Tool: Fetch available tools
    Tool-->>MCPP: Return tool list
    MCPP-->>LLM: Convert to LLM format
    LLM->>MCPP: Execute tool call
    MCPP->>Tool: Call specific tool
    Tool-->>MCPP: Return result
    MCPP-->>LLM: Format result
    LLM-->>ThreadP: Generate final reply
    ThreadP-->>Chat: Display result
```

---

## 🏛️ Architectural Design Principles

### 1. **Layered Architecture**

* **Main Process Layer:** Handles system-level ops, window mgmt, core business logic
* **Renderer Process Layer:** Handles UI, user interactions, frontend state
* **Preload Layer:** Secure IPC bridge

### 2. **Presenter Pattern**

* Each functional domain has a dedicated Presenter class
* Presenters manage business logic & state
* EventBus for loose-coupling communication

### 3. **Multi-Window & Multi-Tab Architecture**

* **Window Shell:** Lightweight tab bar UI
* **Tab Content:** Full app features
* **Isolated Vue Instances:** Separation of concerns & better performance

### 4. **Event-Driven Architecture**

* Unified event naming conventions
* Clear event responsibilities
* Avoid cyclic dependencies and event collisions

---

## 🔧 Core Component Descriptions

### **WindowPresenter & TabPresenter**

* **WindowPresenter:** Manages lifecycle of BrowserWindow instances
* **TabPresenter:** Handles creation, destruction, movement of WebContentsViews
* Supports tab drag-and-drop across windows

### **McpPresenter**

* **ServerManager:** Manages MCP server connections & lifecycle
* **ToolManager:** Caches tool definitions, manages tool call permissions
* **Format Conversion:** Converts between MCP tool format and different LLM providers

### **ThreadPresenter**

* Manages chat session creation, switching, history
* Coordinates LLM calls & message flow
* Handles streaming responses & error recovery

### **ConfigPresenter**

* Central config management: user, model, MCP settings
* Publishes config change events
* Data persistence & migration

---

## 🚀 Getting Started

### 1. **Setup Environment**

```bash
# Install dependencies
pnpm install

# Start development server
pnpm run dev
```

### 2. **Key Development Directories**

* `src/main/presenter/` – Core business logic
* `src/renderer/src/` – Frontend Vue components
* `src/renderer/shell/` – Tab bar UI
* `src/shared/` – Type definitions and shared code

### 3. **Common Development Tasks**

* **Add New Feature:** Create the corresponding Presenter and Vue component
* **Extend MCP Tools:** Add new tool support in McpPresenter
* **UI Component Development:** Use Vue3 + Tailwind CSS in renderer layer
* **Data Persistence:** Use SqlitePresenter or ConfigPresenter

### 4. **Debugging Tips**

* **Main process debugging:** VSCode breakpoints + Electron DevTools
* **Renderer process debugging:** Chrome DevTools
* **MCP tools debugging:** Built-in MCP debug window
* **Event flow debugging:** EventBus logging

---

## 📚 Related Docs

* [Multi-Window Architecture Design](./multi-window-architecture.md)
* [MCP Architecture Doc](./mcp-presenter-architecture.md)
* [Event System Design](./event-system-design.md)
* [Developer Guide](./developer-guide.md)

---

This diagram and guide give developers a global view of the Docomoe project, helping you quickly locate code and understand system operation.

---
